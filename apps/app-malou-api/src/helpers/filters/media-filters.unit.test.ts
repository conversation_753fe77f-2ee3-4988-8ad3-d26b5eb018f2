import { MediaFilters } from './media-filters';

describe('MediaFilters', () => {
    describe('buildQuery', () => {
        it('should escape regex special characters in title search', () => {
            // Test with problematic regex characters that were causing the original issue
            const testCases = [
                'é(',
                'test[',
                'search*',
                'query+',
                'text?',
                'name.',
                'value^',
                'content$',
                'data{',
                'info}',
                'word|',
                'phrase\\',
                'normal text', // should still work with normal text
            ];

            testCases.forEach((title) => {
                const filters = new MediaFilters({
                    restaurantId: 'test-restaurant-id',
                    title,
                    mediaType: 'all',
                    isNeverUsed: false,
                });

                // This should not throw an error
                expect(() => {
                    const query = filters.buildQuery();

                    // Verify the query structure
                    expect(query).toHaveProperty('$and');
                    expect(Array.isArray(query.$and)).toBe(true);

                    // Find the title search condition
                    const titleCondition = query.$and.find((condition: any) => condition.$or);
                    expect(titleCondition).toBeDefined();
                    expect(titleCondition.$or).toHaveLength(3); // title, description, name

                    // Verify that each condition has the escaped regex
                    titleCondition.$or.forEach((orCondition: any) => {
                        const field = Object.keys(orCondition)[0];
                        expect(['title', 'description', 'name']).toContain(field);
                        expect(orCondition[field]).toHaveProperty('$regex');
                        expect(orCondition[field]).toHaveProperty('$options', 'i');
                        expect(orCondition[field]).toHaveProperty('$exists', true);
                        expect(orCondition[field]).toHaveProperty('$ne', null);

                        // The regex should be a string (escaped)
                        expect(typeof orCondition[field].$regex).toBe('string');
                    });
                }).not.toThrow();
            });
        });

        it('should handle diacritics correctly in title search', () => {
            const filters = new MediaFilters({
                restaurantId: 'test-restaurant-id',
                title: 'café',
                mediaType: 'all',
                isNeverUsed: false,
            });

            const query = filters.buildQuery();
            const titleCondition = query.$and.find((condition: any) => condition.$or);

            // The regex should contain diacritic-insensitive patterns
            const regex = titleCondition.$or[0].title.$regex;
            expect(regex).toContain('[eéëè]'); // Should handle 'é' in 'café'
        });

        it('should build correct query structure without title', () => {
            const filters = new MediaFilters({
                restaurantId: 'test-restaurant-id',
                title: '',
                mediaType: 'photo',
                isNeverUsed: true,
            });

            const query = filters.buildQuery();

            expect(query).toHaveProperty('$and');
            expect(Array.isArray(query.$and)).toBe(true);

            // Should not have title search condition
            const titleCondition = query.$and.find((condition: any) => condition.$or);
            expect(titleCondition).toBeUndefined();

            // Should have other conditions
            const typeCondition = query.$and.find((condition: any) => condition.type);
            expect(typeCondition).toEqual({ type: 'photo' });

            const neverUsedCondition = query.$and.find((condition: any) => condition.postIds);
            expect(neverUsedCondition).toEqual({ postIds: [] });
        });

        it('should handle empty title correctly', () => {
            const filters = new MediaFilters({
                restaurantId: 'test-restaurant-id',
                title: null,
                mediaType: 'all',
                isNeverUsed: false,
            });

            expect(() => {
                const query = filters.buildQuery();

                // Should not have title search condition
                const titleCondition = query.$and.find((condition: any) => condition.$or);
                expect(titleCondition).toBeUndefined();
            }).not.toThrow();
        });
    });
});
