// Hyperline API response interfaces based on https://docs.hyperline.co/api-reference/endpoints/customers/get-customer

export interface HyperlineApiCustomer {
    id: string;
    name: string;
    custom_properties: {
        google_place_id: string | null;
        brand_account: boolean | null;
    };
    external_id: string | null; // malouId
    children?: HyperlineApiCustomer[];
}

export interface HyperlineApiOrganization {
    id: string;
    child_customer_ids: string[];
}

export interface HyperlineLocation {
    id: string;
    name: string;
    placeId: string | null; // Google Place ID
    malouRestaurantId: string | null; // If already connected to a Malou restaurant
    isBrandAccount: boolean;
}

export interface HyperlineSubscription {
    id: string;
    customer_id: string;
    status: 'active' | 'inactive' | 'cancelled' | 'past_due';
    current_period_start: string;
    current_period_end: string;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
}

export interface GetLocationsByOrganizationRequest {
    organizationProviderId: string;
}

export interface GetLocationsByOrganizationResponse {
    locations: HyperlineLocation[];
}
