import axios, { AxiosInstance } from 'axios';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';

import { IHyperlineProvider } from './hyperline.provider.interface';
import {
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    HyperlineApiCustomer,
    HyperlineApiOrganization,
    HyperlineLocation,
} from './hyperline.provider.interfaces';

@singleton()
export class HyperlineProvider implements IHyperlineProvider {
    private _axiosInstance: AxiosInstance;

    constructor() {
        this._axiosInstance = axios.create({
            baseURL: Config.vendors.hyperline.baseUrl,
            headers: {
                Authorization: `Bearer ${Config.vendors.hyperline.apiKey}`,
                'Content-Type': 'application/json',
            },
        });
    }

    async _getCustomer(customerId: string): Promise<HyperlineApiCustomer> {
        try {
            const response = await this._axiosInstance.get<HyperlineApiCustomer>(`/customers/${customerId}`);
            return response.data;
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching customer', { customerId, error: error.message });
            throw error;
        }
    }

    private async _getOrganization(customerId: string): Promise<HyperlineApiOrganization> {
        try {
            const response = await this._axiosInstance.get<HyperlineApiOrganization>(`/organisations/${customerId}`);
            return response.data;
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching customer', { customerId, error: error.message });
            throw error;
        }
    }

    async getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse> {
        try {
            const parentOrganization = await this._getOrganization(request.organizationProviderId);
            const legalEntities = await Promise.all(
                parentOrganization.child_customer_ids.map((customerId) => this._getOrganization(customerId))
            );
            const locationIds = legalEntities.flatMap((legalEntity) => legalEntity.child_customer_ids);

            const locations: HyperlineLocation[] = [];

            if (locationIds?.length > 0) {
                for (const locationId of locationIds) {
                    const location = await this._getCustomer(locationId);

                    locations.push({
                        id: location.id,
                        name: location.name,
                        placeId: location.custom_properties.google_place_id,
                        malouRestaurantId: location.external_id,
                        isBrandAccount: !!location.custom_properties.brand_account,
                    });
                }
            }

            return {
                locations,
            };
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching locations by organization', {
                organizationProviderId: request.organizationProviderId,
                error: error.message,
            });
            throw error;
        }
    }
}
