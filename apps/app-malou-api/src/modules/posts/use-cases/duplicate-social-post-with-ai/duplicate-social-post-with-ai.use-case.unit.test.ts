import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostSource } from '@malou-io/package-utils';

import ':helpers/tests/testing-utils';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiPostDuplicationResponse, AiTextDuplicationService } from ':microservices/ai-text-duplication.service';
import { DuplicateSocialPostTextPayload } from ':modules/ai/interfaces/ai.interfaces';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { DuplicateSocialPostWithAiUseCase } from ':modules/posts/use-cases/duplicate-social-post-with-ai/duplicate-social-post-with-ai.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('DuplicateSocialPostWithAiUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository', 'RestaurantsRepository', 'PlatformsRepository']);
    });

    const duplicatedPostCaption = 'A duplicated post caption';

    beforeEach(() => {
        class AiSocialTextDuplicationServiceMock {
            generateDuplication(payload: DuplicateSocialPostTextPayload): Promise<GenericAiServiceResponseType<AiPostDuplicationResponse>> {
                return Promise.resolve({
                    aiResponse: {
                        restaurantName: payload.restaurantData.duplicator.restaurantName,
                        postDescription: `${duplicatedPostCaption} ${payload.restaurantData.duplicator.restaurantName}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                    aiInteractionDetails: [],
                });
            }
        }
        container.register(AiTextDuplicationService, { useValue: new AiSocialTextDuplicationServiceMock() as any });
    });

    it('should throw POST_NOT_FOUND error if post not found', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2({
            seeds: {},
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });
        await testCase.build();
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(
            duplicateSocialPostWithAiUseCase.execute({
                restaurantIds: [],
                postRefToDuplicate: { id: newDbId().toString() },
                userId: newDbId().toString(),
                postDestination: PostSource.SOCIAL,
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should throw RESTAURANT_NOT_FOUND error if post restaurant not found', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'posts'>({
            seeds: {
                platforms: {
                    data() {
                        return [getDefaultPlatform().build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().platformId(dependencies.platforms()[0]._id).build()];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedErrorCode = testCase.getExpectedErrorCode();
        const postId = seededObjects.posts[0]?._id as DbId;

        await expect(
            duplicateSocialPostWithAiUseCase.execute({
                restaurantIds: [],
                postRefToDuplicate: { id: postId.toString() },
                userId: newDbId().toString(),
                postDestination: PostSource.SOCIAL,
            })
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should return list of post captions with restaurants information', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'platforms'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('Restaurant 1').build(),
                            getDefaultRestaurant().name('Restaurant 2').build(),
                            getDefaultRestaurant().name('Restaurant 3').build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.FACEBOOK)
                                .name('plat1')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.INSTAGRAM)
                                .name('plat2')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .key(PlatformKey.FACEBOOK)
                                .name('plat3')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .key(PlatformKey.INSTAGRAM)
                                .name('plat4')
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return [
                    {
                        keys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                        fbPlatformName: dependencies.platforms[0].name,
                        fbPlatformCity: dependencies.platforms[0].address?.locality,
                        fbPlatformId: dependencies.platforms[0].socialId,
                        restaurantId: dependencies.restaurants[0]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[0].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                    {
                        keys: [PlatformKey.FACEBOOK],
                        fbPlatformName: dependencies.platforms[2].name,
                        fbPlatformCity: dependencies.platforms[2].address?.locality,
                        fbPlatformId: dependencies.platforms[2].socialId,
                        restaurantId: dependencies.restaurants[1]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[1].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                    {
                        keys: [PlatformKey.INSTAGRAM],
                        fbPlatformName: null,
                        fbPlatformCity: null,
                        fbPlatformId: null,
                        restaurantId: dependencies.restaurants[2]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[2].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                ];
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString());

        const result = await duplicateSocialPostWithAiUseCase.execute({
            restaurantIds,
            postRefToDuplicate: { id: postId.toString() },
            userId: newDbId().toString(),
            postDestination: PostSource.SOCIAL,
        });

        expect(result).toEqual(expectedResult);
    });

    it('should not include Mapstr Platform if no credentials', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'platforms'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('Restaurant 1').build(),
                            getDefaultRestaurant().name('Restaurant 2').build(),
                            getDefaultRestaurant().name('Restaurant 3').build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.FACEBOOK)
                                .name('plat1')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.MAPSTR)
                                .name('plat2')
                                .credentials([])
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .key(PlatformKey.FACEBOOK)
                                .name('plat3')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .key(PlatformKey.INSTAGRAM)
                                .name('plat4')
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return [
                    {
                        keys: [PlatformKey.FACEBOOK],
                        fbPlatformName: dependencies.platforms[0].name,
                        fbPlatformCity: dependencies.platforms[0].address?.locality,
                        fbPlatformId: dependencies.platforms[0].socialId,
                        restaurantId: dependencies.restaurants[0]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[0].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                    {
                        keys: [PlatformKey.FACEBOOK],
                        fbPlatformName: dependencies.platforms[2].name,
                        fbPlatformCity: dependencies.platforms[2].address?.locality,
                        fbPlatformId: dependencies.platforms[2].socialId,
                        restaurantId: dependencies.restaurants[1]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[1].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                    {
                        keys: [PlatformKey.INSTAGRAM],
                        fbPlatformName: null,
                        fbPlatformCity: null,
                        fbPlatformId: null,
                        restaurantId: dependencies.restaurants[2]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[2].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                ];
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString());

        const result = await duplicateSocialPostWithAiUseCase.execute({
            restaurantIds,
            postRefToDuplicate: { id: postId.toString() },
            userId: newDbId().toString(),
            postDestination: PostSource.SOCIAL,
        });

        expect(result).toEqual(expectedResult);
    });

    it('should include Mapstr Platform if credentials', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'platforms'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('Restaurant 1').build()];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.FACEBOOK)
                                .name('plat1')
                                .build(),
                            getDefaultPlatform()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .key(PlatformKey.MAPSTR)
                                .name('plat2')
                                .credentials([newDbId()])
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return [
                    {
                        keys: [PlatformKey.FACEBOOK, PlatformKey.MAPSTR],
                        fbPlatformName: dependencies.platforms[0].name,
                        fbPlatformCity: dependencies.platforms[0].address?.locality,
                        fbPlatformId: dependencies.platforms[0].socialId,
                        restaurantId: dependencies.restaurants[0]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[0].name}`,
                        hashtags: ['#lenina', '#toto'],
                    },
                ];
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString());

        const result = await duplicateSocialPostWithAiUseCase.execute({
            restaurantIds,
            postRefToDuplicate: { id: postId.toString() },
            userId: newDbId().toString(),
            postDestination: PostSource.SOCIAL,
        });

        expect(result).toEqual(expectedResult);
    });

    it('should return list limited to 150 restaurants', async () => {
        const duplicateSocialPostWithAiUseCase = container.resolve(DuplicateSocialPostWithAiUseCase);

        const maxAcceptedRestaurants = duplicateSocialPostWithAiUseCase.MAX_ALLOWED_RESTAURANTS;
        const arrayOf160 = Array.from({ length: duplicateSocialPostWithAiUseCase.MAX_ALLOWED_RESTAURANTS + 10 });

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('Restaurant 1').build(),
                            ...arrayOf160.map((_, index) => getDefaultRestaurant().name(`Restaurant ${index}`).build()),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).text('A nice post caption').build()];
                    },
                },
            },
            expectedResult: (dependencies) => {
                return arrayOf160
                    .map((_, index) => ({
                        keys: [],
                        fbPlatformName: null,
                        fbPlatformId: null,
                        fbPlatformCity: null,
                        restaurantId: dependencies.restaurants[index + 1]._id.toString(),
                        postCaption: `${duplicatedPostCaption} ${dependencies.restaurants[index + 1].name}`,
                        hashtags: ['#lenina', '#toto'],
                    }))
                    .filter((_, index) => index < maxAcceptedRestaurants);
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();
        const postId = seededObjects.posts[0]?._id as DbId;
        const restaurantIds = seededObjects.restaurants.map((restaurant) => restaurant._id.toString()).filter((_, index) => index !== 0);

        const result = await duplicateSocialPostWithAiUseCase.execute({
            restaurantIds,
            postRefToDuplicate: { id: postId.toString() },
            userId: newDbId().toString(),
            postDestination: PostSource.SOCIAL,
        });

        expect(result.length).toEqual(maxAcceptedRestaurants);
        expect(result).toEqual(expectedResult);
    });
});
